<template>
  <div class="flex gap-10px">
    <Popconfirm title="确认操作？" okText="确认" cancelText="取消" :disabled="updateButton.disabled" @confirm="handleUpdate">
      <Button :disabled="updateButton.disabled">
        {{ updateButton.text }}
      </Button>
    </Popconfirm>
    <Popconfirm title="确认操作？" okText="确认" cancelText="取消" :disabled="restartButton.disabled" @confirm="handleRestart">
      <Button :disabled="restartButton.disabled">
        {{ restartButton.text }}
      </Button>
    </Popconfirm>
    <Popconfirm title="确认操作？" okText="确认" cancelText="取消" :disabled="disableButton.disabled" @confirm="handleDisable">
      <Button :disabled="disableButton.disabled">
        {{ disableButton.text }}
      </Button>
    </Popconfirm>
  </div>
</template>

<script setup lang="ts">
import { But<PERSON>, Popconfirm } from 'ant-design-vue';
import { computed } from 'vue';
import type { InstanceConfigItem } from '../../../api';
import { pendingStateType, workStateType } from './type.data';

const props = withDefaults(defineProps<{
  item: InstanceConfigItem;
}>(), {
  item: () => ({} as InstanceConfigItem),
});

const emit = defineEmits<{
  (event: 'update', instanceId: number, isCancel: boolean): void;
  (event: 'restart', instanceId: number, isCancel: boolean): void;
  (event: 'disable', instanceId: number, isCancel: boolean): void;
}>();

/**
 * 按钮状态配置常量
 * 定义了不同操作按钮在各种状态下的行为规则
 */
const BUTTON_STATES = {
  update: {

    disabledWorkStates: [workStateType.Offline, workStateType.Restarting, workStateType.Updating],

    disabledPendingStates: [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable],

    cancelStates: [pendingStateType.WaitUpdate, pendingStateType.WaitUpdateAndDisable],

    scheduleStates: [pendingStateType.Null, pendingStateType.WaitDisable],
  },
  restart: {

    disabledWorkStates: [workStateType.Offline, workStateType.Restarting],

    cancelStates: [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable],

    scheduleWorkStates: [workStateType.Checking, workStateType.Updating],

    excludeScheduleStates: [pendingStateType.WaitUpdate, pendingStateType.WaitDisable],
  },
  disable: {

    disabledWorkStates: [workStateType.Restarting],

    cancelStates: [
      pendingStateType.WaitDisable,
      pendingStateType.WaitUpdateAndDisable,
      pendingStateType.WaitRestartAndDisable,
    ],
    scheduleStates: [pendingStateType.WaitUpdate, pendingStateType.WaitRestart],
  },
};

/**
 * 更新按钮状态计算
 * 根据实例的工作状态和等待状态计算按钮的禁用状态和显示文本
 */
const updateButton = computed(() => {
  const { workState, pendingState } = props.item;
  const { disabledWorkStates, disabledPendingStates, cancelStates, scheduleStates } = BUTTON_STATES.update;
  const disabled = disabledWorkStates.includes(workState!) || disabledPendingStates.includes(pendingState!);

  let text = '更新';
  if (workState === workStateType.Checking && cancelStates.includes(pendingState!)) {
    text = '取消更新';
  } else if (workState === workStateType.Checking && scheduleStates.includes(pendingState!)) {
    text = '预约更新';
  }

  return { disabled, text };
});

/**
 * 重启按钮状态计算
 */
const restartButton = computed(() => {
  const { workState, pendingState } = props.item;
  const { disabledWorkStates, cancelStates, scheduleWorkStates, excludeScheduleStates } = BUTTON_STATES.restart;

  const disabled = disabledWorkStates.includes(workState!);

  let text = '重启';
  if (scheduleWorkStates.includes(workState!) && cancelStates.includes(pendingState!)) {
    text = '取消重启';
  } else if (scheduleWorkStates.includes(workState!) && !excludeScheduleStates.includes(pendingState!)) {
    text = '预约重启';
  }

  return { disabled, text };
});

/**
 * 禁用/启用按钮状态计算
 */
const disableButton = computed(() => {
  const { workState, pendingState, disabled: itemDisabled } = props.item;
  const { disabledWorkStates, cancelStates, scheduleStates } = BUTTON_STATES.disable;

  const disabled = disabledWorkStates.includes(workState!);

  let text = '禁用';
  if (itemDisabled) {
    text = '启用';
  } else if (cancelStates.includes(pendingState!)) {
    text = '取消禁用';
  } else if (workState === workStateType.Checking && scheduleStates.includes(pendingState!)) {
    text = '预约禁用';
  }

  return { disabled, text };
});

/** 处理更新按钮点击 */
function handleUpdate() {
  const { workState, pendingState } = props.item;
  const isCancel = workState === workStateType.Checking
    && BUTTON_STATES.update.cancelStates.includes(pendingState!);
  emit('update', props.item.id!, isCancel);
}

/** 处理重启按钮点击 */
function handleRestart() {
  const { workState, pendingState } = props.item;
  const isCancel = BUTTON_STATES.restart.scheduleWorkStates.includes(workState!)
    && BUTTON_STATES.restart.cancelStates.includes(pendingState!);
  emit('restart', props.item.id!, isCancel);
}

/** 处理禁用/启用按钮点击 */
function handleDisable() {
  emit('disable', props.item.id!, props.item.disabled!);
}
</script>
