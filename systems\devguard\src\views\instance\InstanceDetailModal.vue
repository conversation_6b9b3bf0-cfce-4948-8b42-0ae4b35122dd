<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="FO-Font-B16">
          <span>提交详情</span>
        </span>
      </div>
    </template>
    <div class="flex flex-col gap-10px p-20px">
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            检查状态：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ dayjs(startTime).format('HH:mm:ss') }} - {{ endTime ? dayjs(endTime).format('HH:mm:ss') : '' }}
          </span>
        </Col>
        <Col :span="4">
          <span :style="checkStateOptions.find(item => item.value === status)?.colorStyle" class="FO-Font-B16">
            {{ checkStateOptions.find(item => item.value === status)?.label }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">  {{ taskItemsDetail?.submitState === SubmitStateType.SubmittingSucceeded ? '正式CL：' : '本地CL：' }}</span>
        </Col>
        <Col :span="16">
          <div class="flex items-center">
            <span>
              {{ taskItemsDetail?.submitState === SubmitStateType.SubmittingSucceeded ? `${taskItemsDetail?.submitCL}` : `${taskItemsDetail?.shelveCL}` }}
            </span>
            <Tooltip placement="bottom" @openChange="getSubmitInfo">
              <template #title>
                <div>提交人:{{ promptText?.submitterName }}</div>
                <div>日期:{{ promptText?.date }}</div>
                <div>描述:{{ promptText?.description }}</div>
              </template>
              <IIcon class="ml-5px" />
            </Tooltip>
          </div>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交人：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ taskItemsDetail?.submitter?.nickName }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            单号：
          </span>
        </Col>
        <Col :span="16">
          <a :href="taskItemsDetail?.workItemURL" target="_blank">
            {{ taskItemsDetail?.workItemTitle }}
          </a>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交状态：
          </span>
        </Col>
        <Col :span="16">
          <div class="b-rd-8px bg-FO-Container-Fill3 p-5px">
            <Process :item="taskItemsDetail" />
          </div>
        </Col>
      </Row>
    </div>
    <template #footer>
      <div class="mt flex justify-center">
        <Button type="primary" @click="jumpsubmitPage(`${taskItemsDetail?.swarmBaseURL}/changes/${taskItemsDetail?.submitState === SubmitStateType.SubmittingSucceeded ? taskItemsDetail?.submitCL : taskItemsDetail?.shelveCL}`)">
          查看提交
        </Button>
        <Button type="primary" class="ml-2" @click="JumpReviewPage(`${taskItemsDetail?.swarmBaseURL}/reviews/${taskItemsDetail?.reviewCL}`)">
          查看审查
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Col, Modal, Row, Tooltip } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import Process from '../commit-list/Process.vue';
import { type RecordListItem, type SubmitInfo, getSubmitInfoApi } from '../../api';
import dayjs from 'dayjs';
import { checkStateOptions } from './type.data';
import { SubmitStateType } from '../commit-list/type.data';
import IIcon from '../../assets/icons/iIcon.svg?component';
import { ref } from 'vue';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { traceClickEvent } from '../../services/track';
import { TrackEventName } from '../../constants/event';

const props = defineProps< ModalBaseProps<{ updatedItem?: null }> & {
  taskItemsDetail?: RecordListItem;
  status?: number;
  endTime?: number;
  startTime?: number;
  recordID?: number;
}>();
const forgeonConfig = useForgeonConfigStore(store);

const { execute: submitInfoExecute, data: submitInfo } = useLatestPromise(getSubmitInfoApi);
const promptText = ref<SubmitInfo>();
function JumpReviewPage(url: string) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_REVIEW_CLICK);
  jumpPage(url);
}
function jumpsubmitPage(url: string) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_DETAIL_CLICK);
  jumpPage(url);
}
function jumpPage(url: string) {
  window.open(url);
}
async function getSubmitInfo(value: boolean) {
  if (!props.recordID) {
    return;
  }
  if (value) {
    await submitInfoExecute({ id: forgeonConfig.currentProjectId!, recordID: props.recordID }, {});
    promptText.value = submitInfo.value?.data?.data;
  }
}
</script>
