<template>
  <div class="w-full flex flex-col items-center gap-[20px] py-[10px]">
    <GanttChart
      class="min-h-60px w-full rd-[12px] bg-FO-Container-Fill3"
      :height="120"
      :items="testGanttItems"
      :min="displayRange[0]"
      :max="displayRange[1]"
      :axisLabelFormat="(percentage) => {
        return dayjs(percentage).format('HH:mm');
      }"
      :rowHeight="15"
      :rowGap="10"
      :tickInterval="tickInterval"
      :precision="60 * 1000 * 10"
      :renderItem="renderItem"
      itemClass="hover:scale-101 min-w-[4px]"
      axisDividerClass="bg-FO-Container-Fill4"
    />
  </div>
</template>

<script lang="tsx" setup>
import { type GanttItem, FollowTooltip, GanttChart } from '@hg-tech/oasis-common';
import { useLatestPromise } from '@hg-tech/utils-vue';
import dayjs, { type Dayjs } from 'dayjs';
import { type TaskItems, getTaskItemsApi } from '../../api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';

import { type PropType, computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { checkStateType } from './type.data';

const props = defineProps({
  instanceID: {
    type: Number as PropType<number>,
    required: true,
  },
  displayRange: {
    type: Array as unknown as PropType<[number, number]>,
    default: () => [0, 0],
  },
  rangePicker: {
    type: Array as unknown as PropType<[Dayjs, Dayjs]>,
    default: () => [0, 0],
  },
});
const emits = defineEmits<{
  (e: 'itemClick', value: GanttItem): void;
}>();
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const { execute: getTaskItemsExecute, data: taskItems } = useLatestPromise(getTaskItemsApi);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const testGanttItems = ref<GanttItem[][]>([]);
watch(() => [props.instanceID, props.rangePicker], async () => {
  await getTaskItemsExecute({
    id: forgeonConfig.currentProjectId!,
    streamID: submitStreamID!,
    instanceID: props.instanceID,
    startTime: props.displayRange[0],
    endTime: props.displayRange[1],
  }, {});
  testGanttItems.value = [];
  const userMap: Record<string, GanttItem[]> = {};
  // 按照distinguishCode分组
  taskItems.value?.data?.data?.forEach((item: TaskItems) => {
    const user = item.distinguishCode!;
    if (!userMap[user]) {
      userMap[user] = [];
    }
    userMap[user].push({
      id: item.clRecordID,
      label: item.title,
      start: item.startTime,
      end: item.endTime,
      meta: {
        title: item.title,
        user,
        status: item.status,
      },
    });
  });

  testGanttItems.value = Object.values(userMap);
}, {
  immediate: true,
  deep: true,
});
function itemClick(item: GanttItem) {
  emits('itemClick', item);
}
const tickInterval = computed(() => {
  // 以小时为单位，间隔为1小时或2小时（以毫秒为单位）
  const range = props.displayRange[1] - props.displayRange[0];
  return range / 12;
});

function renderItem(item: GanttItem) {
  return (
    <FollowTooltip offset={-50} tooltipStyle={{ minWidth: '120px' }}>
      {{
        default: () => (
          <div class={`h-full w-full flex items-center justify-center ${item.meta.status === checkStateType.Checking ? 'bg-FO-Functional-Warning1-Default' : (item.meta.status === checkStateType.Passed ? 'bg-FO-Functional-Success1-Default' : 'bg-FO-Functional-Error1-Default')}`} onClick={() => itemClick(item)}>
            <span class="text-FO-Text-Primary text-[12px]" />
          </div>
        ),
        content: () => (
          <div class="flex flex-col items-center justify-center">
            <div class="text-FO-Text-Primary text-[12px]"> {dayjs(item.start).format('HH:mm:ss')} - {dayjs(item.end).format('HH:mm:ss')}</div>
            <div class="text-FO-Text-Secondary text-[12px]">{item.meta.title}</div>
            <div class="text-FO-Text-Secondary text-[12px]">来自 {item.meta.user}</div>
            <div class="text-FO-Text-Secondary text-[12px]">点击查看详情</div>
          </div>
        ),
      }}
    </FollowTooltip>
  );
}
</script>
