<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-[20px]">
          <div class="flex items-center gap-[10px]">
            <div>实例类型</div><Select v-model:value="instanceType" class="w-[100px]" :options="instanceTypeOptions" @change="instanceTypeChange" />
          </div>
          <Button v-if="hasSetInstance" @click="goToInstanceConfig()">
            <div class="flex items-center">
              <span>实例配置</span><Icon :icon="RightIcon" class="c-FO-Brand-Primary-Default" />
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>
    <div class="m-[20px] flex justify-between gap-[20px] b-rd-[8px] p-[20px] bg-FO-Container-Fill1!">
      <div>
        <div class="FO-Font-B18">
          待检查队列
        </div>
        <div>
          <div v-for="item in checkQueues" v-show="item.queueUsers.length" :key="item.instanceType" class="mb-[10px] flex items-center gap-[20px]">
            <div class="w-[60px] font-size-[16px]">
              {{ instanceTypeOptions.find((i) => i.value === item.instanceType)?.label }}
            </div>
            <div class="flex gap-[10px]">
              <div v-for="(user, index) in item.queueUsers" :key="user.ID" class="b-rd-[4px] bg-FO-Container-Fill2 p-[4px]">
                <span class="FO-Font-B14">{{ index + 1 }}</span> <span>{{ user.nickName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <RangePicker
        v-model:value="rangePickerValue"
        :showTime="{ format: 'HH:mm' }"
        format="YYYY-MM-DD HH:mm"
        :placeholder="['开始时间', '结束时间']"
      />
    </div>
    <div class="overflow-auto" style="height: calc(100vh - 300px)">
      <div v-for="item in checkInstances" :key="item.id" class="m-[20px] flex flex-col gap-[20px] b-rd-[8px] p-[20px] bg-FO-Container-Fill1!">
        <div class="flex justify-between">
          <div class="flex items-center gap-[10px]">
            <div class="h-[10px] w-[10px] b-rd-[5px]" :class="getDotClass(item!)" />
            <div class="FO-Font-B16">
              {{ item.name }}
            </div>
            <div :class="getFontClass(item!)">
              {{ item.disabled ? '已禁用' : workStateOptions.find(i => i.value === item.workState)?.label }}
            </div>
            <div class="ml-[30px] c-FO-Content-Text3">
              类型：{{ instanceTypeOptions.find((i) => i.value === item.instanceType)?.label }}
            </div>
            <div class="c-FO-Content-Text3">
              已运行{{ getRunTime(item.bootTimestamp!) }}
            </div>
          </div>
          <div v-if="item.pendingState === pendingStateType.WaitRestart" class="b-rd-[6px] bg-FO-Container-Fill3 px-[10px] py-[6px] c-FO-Functional-Error1-Default">
            等待重启中
          </div>
        </div>
        <GanttChart :instanceID="item.id" :displayRange="displayRange" :rangePicker="rangePickerValue" @itemClick="itemClick" />
      </div>
      <div v-if="checkInstances.length" class="m-[20px] b-rd-[8px] p-[20px] bg-FO-Container-Fill1!">
        <Slider
          v-model:value="displayRange" class="w-full" :min="rangePickerValue[0].valueOf()" :max="rangePickerValue[1].valueOf()" range
          :tipFormatter="(percentage) => dayjs(percentage).format('YYYY-MM-DD HH:mm')"
        />
      </div>
    </div>

    <InstanceDetailModalHolder />
  </div>
</template>

<script setup lang="ts">
import RightIcon from '@iconify-icons/icon-park-outline/right';
import { Button, RangePicker, Select, Slider } from 'ant-design-vue';
import PageHeader from '../PageHeader.vue';
import GanttChart from './GanttChart.vue';
import { Icon } from '@iconify/vue';
import { onMounted, ref, watch } from 'vue';
import { type GanttItem, getMicroAppData, PlatformEnterPoint, usePermissionCtx } from '@hg-tech/oasis-common';
import { pendingStateType, workStateOptions, workStateType } from './config/type.data';
import dayjs, { type Dayjs } from 'dayjs';
import InstanceDetailModal from './InstanceDetailModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { useRouter } from 'vue-router';
import { TrackEventName } from '../../../src/constants/event';
import { traceClickEvent } from '../../../src/services/track';
import { instanceTypeEnum, instanceTypeOptions } from './type.data';

import { type CheckQueues, type InstanceConfigItem, getTaskItemsDetailApi, getTaskListApi } from '../../api';
import { MergePermission } from '../../constants/premission';
import { checkAdminPermission } from '../../services/permission';
import { useRouteQuery } from '@vueuse/router';

const router = useRouter();
const { execute: getTaskListExecute, data: taskList } = useLatestPromise(getTaskListApi);
const { execute: getTaskItemsDetailExecute, data: taskItemsDetail } = useLatestPromise(getTaskItemsDetailApi);
const [InstanceDetailModalHolder, showInstanceDetailModal] = useModalShow(InstanceDetailModal);
const forgeonConfig = useForgeonConfigStore(store);
const routeParams = router.currentRoute.value.params;
const routeQuery = router.currentRoute.value.query;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const checkQueues = ref<CheckQueues[]>([]);
const checkInstances = ref<InstanceConfigItem[]>([]);
const displayRange = ref<[number, number]>([0, 0]);
const hasSetInstance = ref(false);
const instanceType = ref(routeQuery.instanceType ? Number(routeQuery.instanceType) : instanceTypeEnum.All);
const instanceTypeQuery = useRouteQuery('instanceType', 0, { transform: Number });
const permissionData = getMicroAppData(usePermissionCtx);
const rangePickerValue = ref<[Dayjs, Dayjs]>([dayjs(new Date().getTime() - 24 * 60 * 60), dayjs(new Date())]);
watch(() => rangePickerValue, () => {
  if (rangePickerValue.value) {
    displayRange.value = [rangePickerValue.value[0].valueOf(), rangePickerValue.value[1].valueOf()] as [number, number];
  } else {
    displayRange.value = [0, 0];
  }
}, { immediate: true, deep: true });
function getDotClass(workState: InstanceConfigItem) {
  if (workState.disabled) {
    return 'bg-FO-Functional-Error1-Default';
  }
  switch (workState.workState) {
    case workStateType.Checking:
    case workStateType.Updating:
      return 'bg-FO-Datavis-Yellow2';
    case workStateType.Restarting:
    case workStateType.Offline:
      return 'bg-FO-Content-Text4';
    case workStateType.Idle:
      return 'bg-FO-Datavis-Blue2';
  }
}
function getFontClass(workState: InstanceConfigItem) {
  if (workState.disabled) {
    return 'c-FO-Functional-Error1-Default';
  }
  switch (workState.workState) {
    case workStateType.Checking:
    case workStateType.Updating:
      return 'c-FO-Datavis-Yellow2';
    case workStateType.Restarting:
    case workStateType.Offline:
      return 'c-FO-Content-Text4';
    case workStateType.Idle:
      return 'c-FO-Datavis-Blue2';
  }
}
async function itemClick(item: GanttItem) {
  if (!item.id) {
    return;
  }
  await getTaskItemsDetailExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID!, recordID: item.id! as number }, {});
  await showInstanceDetailModal({
    taskItemsDetail: taskItemsDetail.value?.data?.data,
    status: item.meta.status,
    endTime: item.end,
    startTime: item.start,
    recordID: item.id! as number,
  });
}
function getRunTime(bootTimestamp: number) {
  const start = dayjs(bootTimestamp);
  const end = dayjs(new Date());
  let totalMinutes = end.diff(start, 'minute');
  const days = Math.floor(totalMinutes / (60 * 24));
  totalMinutes -= days * 60 * 24;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes - hours * 60;
  if (days) {
    return ` ${days}天 ${hours}时 ${minutes}分`;
  } else if (hours) {
    return ` ${hours}时 ${minutes}分`;
  } else {
    return ` ${minutes}分`;
  }
}
function instanceTypeChange() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_STATUS_TYPE_SWITCH);
  instanceTypeQuery.value = instanceType.value;
}
function goToInstanceConfig() {
  router.push({
    name: PlatformEnterPoint.InstanceConfig,
    params: {
      submitStreamID,
    },
  });
}
watch(() => instanceType, async () => {
  rangePickerValue.value = [dayjs(new Date().getTime() - 24 * 60 * 60 * 1000), dayjs(new Date())];
  await getTaskListExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID!, instanceType: instanceType.value }, {});
  checkQueues.value = taskList.value?.data?.data?.checkQueues || [];
  checkInstances.value = taskList.value?.data?.data?.checkInstances || [];
}, {
  immediate: true,
  deep: true,
});
onMounted(async () => {
  hasSetInstance.value = checkAdminPermission({ all: [MergePermission.SubmitCenterInstanceStatus, MergePermission.SubmitCenterSetInstance] }, permissionData?.permissionInfo);
});
function goBack() {
  router.push({
    name: PlatformEnterPoint.CommitCenter,
  });
}
</script>

<style lang="less" scoped></style>
